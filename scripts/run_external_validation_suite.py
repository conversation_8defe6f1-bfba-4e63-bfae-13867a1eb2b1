#!/usr/bin/env python3
"""
NEUROGLYPH External Validation Suite
Orchestratore per tutti i test esterni real-world

Esegue in sequenza:
1. Patch Engine Validation (validate_patch_engine.py)
2. Encoder/Decoder Validation (validate_encoder_decoder_external.py)  
3. Reasoning Validation (validate_reasoning_external.py)
4. Genera report consolidato con confronti vs baseline

Target di successo:
- Patch Engine: ≥70% correctness rate
- Encoder/Decoder: ≥90% round-trip, ≥80% AST equivalence
- Reasoning: ≥70% overall accuracy, ≥60% multi-hop accuracy
"""

import os
import sys
import json
import time
import subprocess
from pathlib import Path
from typing import Dict, Any, List
from dataclasses import dataclass, asdict
import tempfile
import shutil

# Aggiungi path NEUROGLYPH
sys.path.insert(0, str(Path(__file__).parent.parent))


@dataclass
class ValidationSuiteResult:
    """Risultato completo della suite di validazione."""
    timestamp: str
    total_duration: float
    
    # Risultati individuali
    patch_engine_result: Dict[str, Any]
    encoder_decoder_result: Dict[str, Any]
    reasoning_result: Dict[str, Any]
    
    # Metriche aggregate
    overall_success: bool
    targets_met: Dict[str, bool]
    summary_metrics: Dict[str, float]
    
    # Confronti
    baseline_comparison: Dict[str, Any]


class ExternalValidationSuite:
    """Suite completa di validazione esterna per NEUROGLYPH."""
    
    def __init__(self, work_dir: str = None):
        self.work_dir = Path(work_dir) if work_dir else Path(tempfile.mkdtemp(prefix="ng_validation_suite_"))
        self.work_dir.mkdir(exist_ok=True)
        
        self.scripts_dir = Path(__file__).parent
        
        # Target di successo
        self.targets = {
            'patch_correctness_rate': 0.70,
            'encoder_round_trip_rate': 0.90,
            'encoder_ast_equivalence_rate': 0.80,
            'reasoning_overall_accuracy': 0.70,
            'reasoning_multi_hop_accuracy': 0.60,
            'humaneval_accuracy': 0.80,
            'hellaswag_accuracy': 0.60
        }
        
        print(f"🏗️ External Validation Suite initialized")
        print(f"📁 Working directory: {self.work_dir}")
        print(f"🎯 Success targets: {self.targets}")
    
    def run_patch_engine_validation(self) -> Dict[str, Any]:
        """Esegue validazione Patch Engine."""
        print("\n🔧 Running Patch Engine Validation...")
        
        script_path = self.scripts_dir / "validate_patch_engine.py"
        output_path = self.work_dir / "patch_validation_report.json"
        
        try:
            # Esegui script
            cmd = [
                sys.executable, str(script_path),
                '--max-files', '20',  # Ridotto per velocità
                '--work-dir', str(self.work_dir / "patch_work"),
                '--output', str(output_path)
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
            
            if result.returncode == 0:
                # Carica risultati
                if output_path.exists():
                    with open(output_path, 'r') as f:
                        return json.load(f)
                else:
                    return {"error": "Output file not created"}
            else:
                return {
                    "error": f"Script failed with code {result.returncode}",
                    "stderr": result.stderr,
                    "stdout": result.stdout
                }
                
        except subprocess.TimeoutExpired:
            return {"error": "Patch engine validation timed out"}
        except Exception as e:
            return {"error": f"Exception running patch validation: {e}"}
    
    def run_encoder_decoder_validation(self) -> Dict[str, Any]:
        """Esegue validazione Encoder/Decoder."""
        print("\n🗜️ Running Encoder/Decoder Validation...")
        
        script_path = self.scripts_dir / "validate_encoder_decoder_external.py"
        output_path = self.work_dir / "encoder_validation_report.json"
        
        try:
            # Esegui script
            cmd = [
                sys.executable, str(script_path),
                '--max-files', '15',  # Ridotto per velocità
                '--work-dir', str(self.work_dir / "encoder_work"),
                '--output', str(output_path)
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=900)
            
            if result.returncode == 0:
                # Carica risultati
                if output_path.exists():
                    with open(output_path, 'r') as f:
                        return json.load(f)
                else:
                    return {"error": "Output file not created"}
            else:
                return {
                    "error": f"Script failed with code {result.returncode}",
                    "stderr": result.stderr,
                    "stdout": result.stdout
                }
                
        except subprocess.TimeoutExpired:
            return {"error": "Encoder/decoder validation timed out"}
        except Exception as e:
            return {"error": f"Exception running encoder validation: {e}"}
    
    def run_reasoning_validation(self) -> Dict[str, Any]:
        """Esegue validazione Reasoning."""
        print("\n🧠 Running Reasoning Validation...")
        
        script_path = self.scripts_dir / "validate_reasoning_external.py"
        output_path = self.work_dir / "reasoning_validation_report.json"
        
        try:
            # Esegui script
            cmd = [
                sys.executable, str(script_path),
                '--output', str(output_path)
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                # Carica risultati
                if output_path.exists():
                    with open(output_path, 'r') as f:
                        return json.load(f)
                else:
                    return {"error": "Output file not created"}
            else:
                return {
                    "error": f"Script failed with code {result.returncode}",
                    "stderr": result.stderr,
                    "stdout": result.stdout
                }
                
        except subprocess.TimeoutExpired:
            return {"error": "Reasoning validation timed out"}
        except Exception as e:
            return {"error": f"Exception running reasoning validation: {e}"}

    def run_humaneval_validation(self) -> Dict[str, Any]:
        """Esegue validazione HumanEval."""
        print("\n🧠 Running HumanEval Validation...")

        script_path = self.scripts_dir / "validate_humaneval_extended.py"
        output_path = self.work_dir / "humaneval_validation_report.json"

        try:
            # Esegui script
            cmd = [
                sys.executable, str(script_path),
                '--output', str(output_path)
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)

            if result.returncode == 0:
                # Carica risultati
                if output_path.exists():
                    with open(output_path, 'r') as f:
                        return json.load(f)
                else:
                    return {"error": "Output file not created"}
            else:
                return {
                    "error": f"Script failed with code {result.returncode}",
                    "stderr": result.stderr,
                    "stdout": result.stdout
                }

        except subprocess.TimeoutExpired:
            return {"error": "HumanEval validation timed out"}
        except Exception as e:
            return {"error": f"Exception running HumanEval validation: {e}"}

    def run_hellaswag_validation(self) -> Dict[str, Any]:
        """Esegue validazione HellaSwag."""
        print("\n🤔 Running HellaSwag Validation...")

        script_path = self.scripts_dir / "validate_hellaswag_extended.py"
        output_path = self.work_dir / "hellaswag_validation_report.json"

        try:
            # Esegui script
            cmd = [
                sys.executable, str(script_path),
                '--output', str(output_path)
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)

            if result.returncode == 0:
                # Carica risultati
                if output_path.exists():
                    with open(output_path, 'r') as f:
                        return json.load(f)
                else:
                    return {"error": "Output file not created"}
            else:
                return {
                    "error": f"Script failed with code {result.returncode}",
                    "stderr": result.stderr,
                    "stdout": result.stdout
                }

        except subprocess.TimeoutExpired:
            return {"error": "HellaSwag validation timed out"}
        except Exception as e:
            return {"error": f"Exception running HellaSwag validation: {e}"}
    
    def extract_key_metrics(self, patch_result: Dict, encoder_result: Dict, reasoning_result: Dict,
                           humaneval_result: Dict, hellaswag_result: Dict) -> Dict[str, float]:
        """Estrae metriche chiave dai risultati."""
        metrics = {}
        
        # Patch Engine metrics
        if 'summary' in patch_result:
            metrics['patch_correctness_rate'] = patch_result['summary'].get('patch_correctness_rate', 0.0)
            metrics['patch_application_rate'] = patch_result['summary'].get('patch_application_rate', 0.0)
            metrics['patch_avg_time'] = patch_result['summary'].get('avg_patch_time', 0.0)
        
        # Encoder/Decoder metrics
        if 'summary' in encoder_result:
            metrics['encoder_round_trip_rate'] = encoder_result['summary'].get('round_trip_success_rate', 0.0)
            metrics['encoder_ast_equivalence_rate'] = encoder_result['summary'].get('ast_equivalence_rate', 0.0)
            metrics['encoder_compression_ratio'] = encoder_result['summary'].get('avg_compression_ratio', 0.0)
            metrics['encoder_avg_time'] = encoder_result['summary'].get('avg_encoding_time', 0.0)
        
        # Reasoning metrics
        if 'summary' in reasoning_result:
            metrics['reasoning_overall_accuracy'] = reasoning_result['summary'].get('overall_accuracy', 0.0)
            metrics['reasoning_success_rate'] = reasoning_result['summary'].get('reasoning_success_rate', 0.0)
            metrics['reasoning_avg_time'] = reasoning_result['summary'].get('avg_reasoning_time', 0.0)
            
        if 'multi_hop_analysis' in reasoning_result:
            metrics['reasoning_multi_hop_accuracy'] = reasoning_result['multi_hop_analysis'].get('multi_hop_accuracy', 0.0)
        
        return metrics
    
    def check_targets_met(self, metrics: Dict[str, float]) -> Dict[str, bool]:
        """Verifica se i target sono stati raggiunti."""
        targets_met = {}
        
        for target_name, target_value in self.targets.items():
            actual_value = metrics.get(target_name, 0.0)
            targets_met[target_name] = actual_value >= target_value
        
        return targets_met
    
    def generate_baseline_comparison(self, metrics: Dict[str, float]) -> Dict[str, Any]:
        """Genera confronto con baseline (valori teorici)."""
        # Baseline teorici per confronto
        baselines = {
            'patch_correctness_rate': 0.30,  # autopep8/pylint tipico
            'encoder_round_trip_rate': 0.95,  # gzip/brotli (sempre perfetto ma non semantico)
            'encoder_ast_equivalence_rate': 0.99,  # AST parser standard
            'reasoning_overall_accuracy': 0.40,  # LLM standard su LogiQA
            'reasoning_multi_hop_accuracy': 0.25  # Multi-hop reasoning tipico
        }
        
        comparison = {}
        for metric, baseline in baselines.items():
            actual = metrics.get(metric, 0.0)
            comparison[metric] = {
                'neuroglyph': actual,
                'baseline': baseline,
                'improvement_factor': actual / baseline if baseline > 0 else 0,
                'improvement_percentage': ((actual - baseline) / baseline * 100) if baseline > 0 else 0
            }
        
        return comparison
    
    def run_full_suite(self) -> ValidationSuiteResult:
        """Esegue l'intera suite di validazione."""
        print("🚀 Starting NEUROGLYPH External Validation Suite...")
        start_time = time.time()
        
        # Esegui validazioni
        patch_result = self.run_patch_engine_validation()
        encoder_result = self.run_encoder_decoder_validation()
        reasoning_result = self.run_reasoning_validation()
        
        total_duration = time.time() - start_time
        
        # Estrai metriche
        metrics = self.extract_key_metrics(patch_result, encoder_result, reasoning_result)
        
        # Verifica target
        targets_met = self.check_targets_met(metrics)
        overall_success = all(targets_met.values())
        
        # Genera confronti
        baseline_comparison = self.generate_baseline_comparison(metrics)
        
        # Crea risultato finale
        result = ValidationSuiteResult(
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S"),
            total_duration=total_duration,
            patch_engine_result=patch_result,
            encoder_decoder_result=encoder_result,
            reasoning_result=reasoning_result,
            overall_success=overall_success,
            targets_met=targets_met,
            summary_metrics=metrics,
            baseline_comparison=baseline_comparison
        )
        
        return result
    
    def print_summary_report(self, result: ValidationSuiteResult):
        """Stampa report riassuntivo."""
        print(f"\n" + "="*80)
        print(f"🎯 NEUROGLYPH EXTERNAL VALIDATION SUITE RESULTS")
        print(f"="*80)
        print(f"⏱️ Total duration: {result.total_duration:.1f}s")
        print(f"📅 Timestamp: {result.timestamp}")
        print(f"🏆 Overall success: {'✅ PASS' if result.overall_success else '❌ FAIL'}")
        
        print(f"\n📊 KEY METRICS:")
        for metric, value in result.summary_metrics.items():
            target = self.targets.get(metric, 0)
            status = "✅" if result.targets_met.get(metric, False) else "❌"
            print(f"   {status} {metric}: {value:.2%} (target: {target:.0%})")
        
        print(f"\n🔍 TARGETS MET:")
        met_count = sum(result.targets_met.values())
        total_targets = len(result.targets_met)
        print(f"   {met_count}/{total_targets} targets achieved ({met_count/total_targets:.1%})")
        
        print(f"\n📈 BASELINE COMPARISON:")
        for metric, comparison in result.baseline_comparison.items():
            improvement = comparison['improvement_percentage']
            if improvement > 0:
                print(f"   ✅ {metric}: +{improvement:.1f}% vs baseline")
            else:
                print(f"   ❌ {metric}: {improvement:.1f}% vs baseline")
        
        print(f"\n" + "="*80)


def main():
    """Funzione principale."""
    import argparse
    
    parser = argparse.ArgumentParser(description="NEUROGLYPH External Validation Suite")
    parser.add_argument('--work-dir', type=str, help='Working directory for all tests')
    parser.add_argument('--output', type=str, default='external_validation_suite_report.json', help='Output report file')
    
    args = parser.parse_args()
    
    # Crea suite
    suite = ExternalValidationSuite(work_dir=args.work_dir)
    
    try:
        # Esegui suite completa
        result = suite.run_full_suite()
        
        # Salva report completo
        with open(args.output, 'w') as f:
            json.dump(asdict(result), f, indent=2)
        
        # Stampa summary
        suite.print_summary_report(result)
        
        print(f"\n📄 Full report saved to: {args.output}")
        
        # Exit code basato su successo
        sys.exit(0 if result.overall_success else 1)
        
    except KeyboardInterrupt:
        print("\n⚠️ Validation suite interrupted by user")
        sys.exit(2)
    except Exception as e:
        print(f"❌ Validation suite failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(3)
    finally:
        # Cleanup
        if suite.work_dir.exists():
            print(f"🧹 Cleaning up {suite.work_dir}")
            shutil.rmtree(suite.work_dir, ignore_errors=True)


if __name__ == '__main__':
    main()
