#!/usr/bin/env python3
"""
Quick test per verificare che la External Validation Suite funzioni
Test rapido senza download di repository esterni
"""

import sys
import json
from pathlib import Path

# Aggiungi path NEUROGLYPH
sys.path.insert(0, str(Path(__file__).parent.parent))

def test_reasoning_validator():
    """Test rapido del reasoning validator."""
    print("🧠 Testing Reasoning Validator...")
    
    try:
        from scripts.validate_reasoning_external import ExternalReasoningValidator
        
        validator = ExternalReasoningValidator()
        
        # Test su un singolo problema
        test_problem = {
            'id': 'test_001',
            'difficulty': 'easy',
            'problem': 'If it rains, the ground gets wet. It is raining. What happens to the ground?',
            'expected_answer': 'wet',
            'reasoning_type': 'modus_ponens'
        }
        
        result = validator.test_logical_reasoning(test_problem)
        
        print(f"  ✅ Reasoning test completed")
        print(f"     - Success: {result.reasoning_success}")
        print(f"     - Steps: {result.steps_count}")
        print(f"     - Time: {result.reasoning_time:.3f}s")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Reasoning test failed: {e}")
        return False

def test_math_reasoning():
    """Test rapido del math reasoning."""
    print("🔢 Testing Math Reasoning...")
    
    try:
        from scripts.validate_reasoning_external import ExternalReasoningValidator
        
        validator = ExternalReasoningValidator()
        
        # Test su un problema matematico
        test_problem = {
            'id': 'math_test_001',
            'difficulty': 'easy',
            'problem': 'Find the value of x if 2x + 3 = 11',
            'expected_answer': 4,
            'reasoning_type': 'algebraic_equation'
        }
        
        result = validator.test_math_reasoning(test_problem)
        
        print(f"  ✅ Math test completed")
        print(f"     - Success: {result.reasoning_success}")
        print(f"     - Answer: {result.predicted_answer}")
        print(f"     - Correct: {result.answer_correct}")
        print(f"     - Steps: {result.steps_count}")
        
        return result.answer_correct
        
    except Exception as e:
        print(f"  ❌ Math test failed: {e}")
        return False

def test_imports():
    """Test degli import principali."""
    print("📦 Testing Imports...")
    
    imports_to_test = [
        "neuroglyph.cognitive.reasoner.NGReasoner",
        "neuroglyph.core.parser.ng_parser.NGParser",
        "neuroglyph.cognitive.adaptive_patcher.NGAdaptivePatcher",
        "neuroglyph.core.encoder.encoder.NeuroglyphEncoder",
        "neuroglyph.core.decoder.decoder.NeuroglyphDecoder"
    ]
    
    success_count = 0
    
    for import_path in imports_to_test:
        try:
            module_path, class_name = import_path.rsplit('.', 1)
            module = __import__(module_path, fromlist=[class_name])
            getattr(module, class_name)
            print(f"  ✅ {import_path}")
            success_count += 1
        except Exception as e:
            print(f"  ❌ {import_path}: {e}")
    
    print(f"  📊 Import success: {success_count}/{len(imports_to_test)}")
    return success_count == len(imports_to_test)

def main():
    """Funzione principale."""
    print("🚀 NEUROGLYPH External Validation Quick Test")
    print("=" * 60)
    
    tests = [
        ("Imports", test_imports),
        ("Math Reasoning", test_math_reasoning),
        ("Logical Reasoning", test_reasoning_validator)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running {test_name} test...")
        try:
            success = test_func()
            results.append((test_name, success))
            status = "✅ PASS" if success else "❌ FAIL"
            print(f"   {status}")
        except Exception as e:
            print(f"   ❌ FAIL: {e}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n" + "=" * 60)
    print(f"📊 QUICK TEST RESULTS:")
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✅" if success else "❌"
        print(f"   {status} {test_name}")
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed ({passed/total:.1%})")
    
    if passed == total:
        print("✅ All tests passed! External validation suite is ready.")
        return 0
    else:
        print("❌ Some tests failed. Check the errors above.")
        return 1

if __name__ == '__main__':
    sys.exit(main())
