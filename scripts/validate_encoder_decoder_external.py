#!/usr/bin/env python3
"""
NEUROGLYPH External Encoder/Decoder Validation
Test del sistema di encoding/decoding simbolico su codebase reali

Obiettivi:
- Testare round-trip fidelity su file Python reali da GitHub
- Misurare compression ratio su codice reale vs sintetico
- Validare AST equivalence su progetti open-source
- Confrontare con altri sistemi di compressione (gzip, brotli)
"""

import os
import sys
import json
import time
import tempfile
import requests
import zipfile
import gzip
import brotli
from pathlib import Path
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass, asdict
import ast
import hashlib

# Aggiungi path NEUROGLYPH
sys.path.insert(0, str(Path(__file__).parent.parent))

try:
    from neuroglyph.core.encoder.encoder import NeuroglyphEncoder
    from neuroglyph.core.decoder.decoder import NeuroglyphDecoder
    from neuroglyph.core.parser.ng_parser import NGParser
    NEUROGLYPH_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ NEUROGLYPH non disponibile: {e}")
    NEUROGLYPH_AVAILABLE = False


@dataclass
class EncodingTestResult:
    """Risultato del test di encoding."""
    file_path: str
    file_size: int
    lines_count: int
    
    # Encoding results
    encoding_success: bool
    encoding_time: float
    compressed_size: int
    compression_ratio: float
    symbols_count: int
    
    # Decoding results
    decoding_success: bool
    decoding_time: float
    round_trip_success: bool
    
    # AST comparison
    ast_equivalent: bool
    ast_diff_count: int
    
    # Comparison with other compressors
    gzip_size: int
    brotli_size: int
    gzip_ratio: float
    brotli_ratio: float
    
    # Error info
    error_message: Optional[str] = None


class ExternalEncoderValidator:
    """Validatore esterno per encoder/decoder NEUROGLYPH."""
    
    def __init__(self, work_dir: str = None):
        self.work_dir = Path(work_dir) if work_dir else Path(tempfile.mkdtemp(prefix="ng_encoder_test_"))
        self.work_dir.mkdir(exist_ok=True)
        
        # Repository per test diversificati
        self.test_repositories = [
            {
                'name': 'scikit-learn',
                'url': 'https://github.com/scikit-learn/scikit-learn/archive/refs/heads/main.zip',
                'python_files_pattern': 'scikit-learn-main/sklearn/utils/*.py',
                'description': 'Machine learning library - complex algorithms'
            },
            {
                'name': 'fastapi',
                'url': 'https://github.com/tiangolo/fastapi/archive/refs/heads/main.zip',
                'python_files_pattern': 'fastapi-main/fastapi/*.py',
                'description': 'Web framework - decorators and async'
            },
            {
                'name': 'pandas',
                'url': 'https://github.com/pandas-dev/pandas/archive/refs/heads/main.zip',
                'python_files_pattern': 'pandas-main/pandas/core/*.py',
                'description': 'Data analysis - complex data structures'
            }
        ]
        
        if NEUROGLYPH_AVAILABLE:
            self.encoder = NeuroglyphEncoder()
            self.decoder = NeuroglyphDecoder()
            self.parser = NGParser()
        
        print(f"📁 Working directory: {self.work_dir}")
    
    def download_repositories(self) -> List[Path]:
        """Scarica repository di test."""
        print("📥 Downloading test repositories...")
        downloaded_files = []
        
        for repo in self.test_repositories:
            try:
                print(f"  📦 Downloading {repo['name']} ({repo['description']})...")
                
                # Download ZIP
                response = requests.get(repo['url'], timeout=60)
                response.raise_for_status()
                
                zip_path = self.work_dir / f"{repo['name']}.zip"
                with open(zip_path, 'wb') as f:
                    f.write(response.content)
                
                # Extract
                with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                    zip_ref.extractall(self.work_dir)
                
                # Find Python files
                import glob
                pattern = str(self.work_dir / repo['python_files_pattern'])
                py_files = glob.glob(pattern)
                
                # Filter files by size (skip very large files)
                filtered_files = []
                for f in py_files:
                    try:
                        size = os.path.getsize(f)
                        if 1000 <= size <= 50000:  # Between 1KB and 50KB
                            filtered_files.append(f)
                    except:
                        continue
                
                # Limit to 8 files per repository
                selected_files = filtered_files[:8]
                downloaded_files.extend([Path(f) for f in selected_files])
                
                print(f"    ✅ {len(selected_files)} files from {repo['name']}")
                
            except Exception as e:
                print(f"    ❌ Failed to download {repo['name']}: {e}")
        
        print(f"📊 Total files downloaded: {len(downloaded_files)}")
        return downloaded_files
    
    def compare_ast_trees(self, original_code: str, decoded_code: str) -> Tuple[bool, int]:
        """Confronta due AST per equivalenza."""
        try:
            original_ast = ast.parse(original_code)
            decoded_ast = ast.parse(decoded_code)
            
            # Semplice confronto tramite dump
            original_dump = ast.dump(original_ast, indent=2)
            decoded_dump = ast.dump(decoded_ast, indent=2)
            
            equivalent = original_dump == decoded_dump
            
            # Conta differenze (approssimativo)
            if not equivalent:
                original_lines = original_dump.split('\n')
                decoded_lines = decoded_dump.split('\n')
                diff_count = abs(len(original_lines) - len(decoded_lines))
                
                # Conta linee diverse
                min_len = min(len(original_lines), len(decoded_lines))
                for i in range(min_len):
                    if original_lines[i] != decoded_lines[i]:
                        diff_count += 1
            else:
                diff_count = 0
            
            return equivalent, diff_count
            
        except Exception:
            return False, -1
    
    def compress_with_standard_tools(self, code: str) -> Tuple[int, int]:
        """Comprimi con gzip e brotli per confronto."""
        code_bytes = code.encode('utf-8')
        
        # Gzip compression
        gzip_compressed = gzip.compress(code_bytes, compresslevel=9)
        gzip_size = len(gzip_compressed)
        
        # Brotli compression
        brotli_compressed = brotli.compress(code_bytes, quality=11)
        brotli_size = len(brotli_compressed)
        
        return gzip_size, brotli_size
    
    def test_encoding_on_file(self, file_path: Path) -> EncodingTestResult:
        """Testa encoding/decoding su un singolo file."""
        if not NEUROGLYPH_AVAILABLE:
            return EncodingTestResult(
                file_path=str(file_path),
                file_size=0,
                lines_count=0,
                encoding_success=False,
                encoding_time=0.0,
                compressed_size=0,
                compression_ratio=0.0,
                symbols_count=0,
                decoding_success=False,
                decoding_time=0.0,
                round_trip_success=False,
                ast_equivalent=False,
                ast_diff_count=-1,
                gzip_size=0,
                brotli_size=0,
                gzip_ratio=0.0,
                brotli_ratio=0.0,
                error_message="NEUROGLYPH not available"
            )
        
        try:
            # Leggi file
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                original_code = f.read()
            
            file_size = len(original_code.encode('utf-8'))
            lines_count = len(original_code.split('\n'))
            
            # Test encoding
            start_time = time.perf_counter()
            encoding_result = self.encoder.encode(original_code)
            encoding_time = time.perf_counter() - start_time
            
            if not encoding_result or not hasattr(encoding_result, 'compressed_symbols'):
                return EncodingTestResult(
                    file_path=str(file_path),
                    file_size=file_size,
                    lines_count=lines_count,
                    encoding_success=False,
                    encoding_time=encoding_time,
                    compressed_size=0,
                    compression_ratio=0.0,
                    symbols_count=0,
                    decoding_success=False,
                    decoding_time=0.0,
                    round_trip_success=False,
                    ast_equivalent=False,
                    ast_diff_count=-1,
                    gzip_size=0,
                    brotli_size=0,
                    gzip_ratio=0.0,
                    brotli_ratio=0.0,
                    error_message="Encoding failed"
                )
            
            # Calcola metriche encoding
            compressed_symbols = encoding_result.compressed_symbols
            symbols_count = len(compressed_symbols)
            compressed_size = len(str(compressed_symbols).encode('utf-8'))
            compression_ratio = compressed_size / file_size if file_size > 0 else 0
            
            # Test decoding
            start_time = time.perf_counter()
            decoding_result = self.decoder.decode(
                compressed_symbols,
                encoding_result.metadata if hasattr(encoding_result, 'metadata') else {}
            )
            decoding_time = time.perf_counter() - start_time
            
            decoding_success = decoding_result and hasattr(decoding_result, 'decoded_code')
            decoded_code = decoding_result.decoded_code if decoding_success else ""
            
            # Round-trip test
            round_trip_success = decoding_success and len(decoded_code.strip()) > 0
            
            # AST comparison
            ast_equivalent, ast_diff_count = self.compare_ast_trees(original_code, decoded_code)
            
            # Standard compression comparison
            gzip_size, brotli_size = self.compress_with_standard_tools(original_code)
            gzip_ratio = gzip_size / file_size if file_size > 0 else 0
            brotli_ratio = brotli_size / file_size if file_size > 0 else 0
            
            return EncodingTestResult(
                file_path=str(file_path),
                file_size=file_size,
                lines_count=lines_count,
                encoding_success=True,
                encoding_time=encoding_time,
                compressed_size=compressed_size,
                compression_ratio=compression_ratio,
                symbols_count=symbols_count,
                decoding_success=decoding_success,
                decoding_time=decoding_time,
                round_trip_success=round_trip_success,
                ast_equivalent=ast_equivalent,
                ast_diff_count=ast_diff_count,
                gzip_size=gzip_size,
                brotli_size=brotli_size,
                gzip_ratio=gzip_ratio,
                brotli_ratio=brotli_ratio
            )
            
        except Exception as e:
            return EncodingTestResult(
                file_path=str(file_path),
                file_size=0,
                lines_count=0,
                encoding_success=False,
                encoding_time=0.0,
                compressed_size=0,
                compression_ratio=0.0,
                symbols_count=0,
                decoding_success=False,
                decoding_time=0.0,
                round_trip_success=False,
                ast_equivalent=False,
                ast_diff_count=-1,
                gzip_size=0,
                brotli_size=0,
                gzip_ratio=0.0,
                brotli_ratio=0.0,
                error_message=str(e)
            )

    def run_validation_batch(self, max_files: int = 24) -> Dict[str, Any]:
        """Esegue validazione batch su file esterni."""
        print("🚀 Starting External Encoder/Decoder Validation...")

        # Download repository
        files = self.download_repositories()

        if not files:
            print("❌ No files downloaded, aborting test")
            return {"error": "No files downloaded"}

        # Limita numero di file
        test_files = files[:max_files]
        print(f"🎯 Testing on {len(test_files)} files")

        results = []

        for i, file_path in enumerate(test_files):
            print(f"📝 Testing file {i+1}/{len(test_files)}: {file_path.name}")
            result = self.test_encoding_on_file(file_path)
            results.append(result)

            # Progress info
            if result.encoding_success:
                print(f"  ✅ Encoded: {result.symbols_count} symbols, {result.compression_ratio:.1%} compression")
                if result.round_trip_success:
                    print(f"  🔄 Round-trip: {'✅' if result.ast_equivalent else '⚠️'} AST equivalent")
                else:
                    print(f"  ❌ Round-trip failed")
            else:
                print(f"  ❌ Encoding failed: {result.error_message}")

        # Analizza risultati
        return self.analyze_results(results)

    def analyze_results(self, results: List[EncodingTestResult]) -> Dict[str, Any]:
        """Analizza i risultati dei test."""
        total_tests = len(results)

        # Filtra risultati validi
        valid_results = [r for r in results if r.encoding_success]

        # Metriche base
        encoding_successes = len(valid_results)
        decoding_successes = len([r for r in valid_results if r.decoding_success])
        round_trip_successes = len([r for r in valid_results if r.round_trip_success])
        ast_equivalences = len([r for r in valid_results if r.ast_equivalent])

        # Performance metriche
        if valid_results:
            avg_encoding_time = sum(r.encoding_time for r in valid_results) / len(valid_results)
            avg_decoding_time = sum(r.decoding_time for r in valid_results) / len(valid_results)
            avg_compression_ratio = sum(r.compression_ratio for r in valid_results) / len(valid_results)
            avg_symbols_count = sum(r.symbols_count for r in valid_results) / len(valid_results)

            # Confronto con compressori standard
            avg_gzip_ratio = sum(r.gzip_ratio for r in valid_results) / len(valid_results)
            avg_brotli_ratio = sum(r.brotli_ratio for r in valid_results) / len(valid_results)

            # File size distribution
            small_files = [r for r in valid_results if r.file_size < 5000]
            medium_files = [r for r in valid_results if 5000 <= r.file_size < 20000]
            large_files = [r for r in valid_results if r.file_size >= 20000]
        else:
            avg_encoding_time = avg_decoding_time = avg_compression_ratio = 0
            avg_symbols_count = avg_gzip_ratio = avg_brotli_ratio = 0
            small_files = medium_files = large_files = []

        # Report dettagliato
        report = {
            'summary': {
                'total_tests': total_tests,
                'encoding_successes': encoding_successes,
                'decoding_successes': decoding_successes,
                'round_trip_successes': round_trip_successes,
                'ast_equivalences': ast_equivalences,
                'encoding_success_rate': encoding_successes / total_tests if total_tests > 0 else 0,
                'decoding_success_rate': decoding_successes / total_tests if total_tests > 0 else 0,
                'round_trip_success_rate': round_trip_successes / total_tests if total_tests > 0 else 0,
                'ast_equivalence_rate': ast_equivalences / total_tests if total_tests > 0 else 0,
                'avg_encoding_time': avg_encoding_time,
                'avg_decoding_time': avg_decoding_time,
                'avg_compression_ratio': avg_compression_ratio,
                'avg_symbols_count': avg_symbols_count
            },
            'compression_comparison': {
                'neuroglyph_ratio': avg_compression_ratio,
                'gzip_ratio': avg_gzip_ratio,
                'brotli_ratio': avg_brotli_ratio,
                'vs_gzip_factor': avg_gzip_ratio / avg_compression_ratio if avg_compression_ratio > 0 else 0,
                'vs_brotli_factor': avg_brotli_ratio / avg_compression_ratio if avg_compression_ratio > 0 else 0
            },
            'file_size_analysis': {
                'small_files': {
                    'count': len(small_files),
                    'avg_compression': sum(r.compression_ratio for r in small_files) / len(small_files) if small_files else 0,
                    'ast_equivalence_rate': len([r for r in small_files if r.ast_equivalent]) / len(small_files) if small_files else 0
                },
                'medium_files': {
                    'count': len(medium_files),
                    'avg_compression': sum(r.compression_ratio for r in medium_files) / len(medium_files) if medium_files else 0,
                    'ast_equivalence_rate': len([r for r in medium_files if r.ast_equivalent]) / len(medium_files) if medium_files else 0
                },
                'large_files': {
                    'count': len(large_files),
                    'avg_compression': sum(r.compression_ratio for r in large_files) / len(large_files) if large_files else 0,
                    'ast_equivalence_rate': len([r for r in large_files if r.ast_equivalent]) / len(large_files) if large_files else 0
                }
            },
            'detailed_results': [asdict(r) for r in results]
        }

        return report


def main():
    """Funzione principale."""
    import argparse
    import shutil

    parser = argparse.ArgumentParser(description="NEUROGLYPH External Encoder/Decoder Validation")
    parser.add_argument('--max-files', type=int, default=24, help='Maximum number of files to test')
    parser.add_argument('--work-dir', type=str, help='Working directory for downloads')
    parser.add_argument('--output', type=str, default='encoder_validation_report.json', help='Output report file')

    args = parser.parse_args()

    # Crea validatore
    validator = ExternalEncoderValidator(work_dir=args.work_dir)

    try:
        # Esegui validazione
        results = validator.run_validation_batch(max_files=args.max_files)

        # Salva report
        with open(args.output, 'w') as f:
            json.dump(results, f, indent=2)

        # Stampa summary
        if 'summary' in results:
            summary = results['summary']
            compression = results.get('compression_comparison', {})

            print(f"\n📊 EXTERNAL ENCODER/DECODER VALIDATION RESULTS:")
            print(f"   - Total tests: {summary['total_tests']}")
            print(f"   - Encoding success: {summary['encoding_successes']} ({summary['encoding_success_rate']:.2%})")
            print(f"   - Round-trip success: {summary['round_trip_successes']} ({summary['round_trip_success_rate']:.2%})")
            print(f"   - AST equivalence: {summary['ast_equivalences']} ({summary['ast_equivalence_rate']:.2%})")
            print(f"   - Avg compression ratio: {summary['avg_compression_ratio']:.1%}")
            print(f"   - Avg encoding time: {summary['avg_encoding_time']:.3f}s")
            print(f"   - Avg symbols count: {summary['avg_symbols_count']:.1f}")

            if compression:
                print(f"\n🗜️ COMPRESSION COMPARISON:")
                print(f"   - NEUROGLYPH: {compression['neuroglyph_ratio']:.1%}")
                print(f"   - Gzip: {compression['gzip_ratio']:.1%}")
                print(f"   - Brotli: {compression['brotli_ratio']:.1%}")
                print(f"   - vs Gzip: {compression['vs_gzip_factor']:.1f}x")
                print(f"   - vs Brotli: {compression['vs_brotli_factor']:.1f}x")

            # Target validation
            target_round_trip = 0.90  # 90% round-trip success
            target_ast_equiv = 0.80   # 80% AST equivalence

            print(f"\n🎯 TARGET VALIDATION:")
            if summary['round_trip_success_rate'] >= target_round_trip:
                print(f"✅ ROUND-TRIP TARGET: {summary['round_trip_success_rate']:.2%} ≥ {target_round_trip:.0%}")
            else:
                print(f"❌ ROUND-TRIP TARGET: {summary['round_trip_success_rate']:.2%} < {target_round_trip:.0%}")

            if summary['ast_equivalence_rate'] >= target_ast_equiv:
                print(f"✅ AST EQUIVALENCE TARGET: {summary['ast_equivalence_rate']:.2%} ≥ {target_ast_equiv:.0%}")
            else:
                print(f"❌ AST EQUIVALENCE TARGET: {summary['ast_equivalence_rate']:.2%} < {target_ast_equiv:.0%}")

        print(f"📄 Full report saved to: {args.output}")

    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # Cleanup
        if validator.work_dir.exists():
            print(f"🧹 Cleaning up {validator.work_dir}")
            shutil.rmtree(validator.work_dir, ignore_errors=True)


if __name__ == '__main__':
    main()
